from gi.repository import GLib
import psutil
import subprocess
import re

import utils.icons as icons
from fabric.bluetooth import BluetoothClient
from fabric.utils import get_relative_path
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.svg import Svg
from services.battery import Battery as BatteryService
from services.network import NetworkClient


class WifiIndicator(Button):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        self.wifi_icon = Svg(
            name="indicators-icon",
            size=24,
            svg_file=get_relative_path("../../../config/assets/icons/wifi.svg"),
        )

        self.add(self.wifi_icon)

        # Start periodic updates using psutil
        GLib.timeout_add_seconds(5, self.update_state)
        self.update_state()

    def get_wifi_info(self):
        """Get WiFi information using psutil and system commands"""
        try:
            # Get network interfaces
            net_if_stats = psutil.net_if_stats()
            net_if_addrs = psutil.net_if_addrs()

            # Find wireless interfaces
            wifi_interfaces = []
            for interface_name, stats in net_if_stats.items():
                if interface_name.startswith(('wl', 'wlan', 'wifi')):
                    wifi_interfaces.append(interface_name)

            if not wifi_interfaces:
                return None, "No WiFi device found", False

            # Use the first WiFi interface found
            wifi_interface = wifi_interfaces[0]

            # Check if interface is up
            if not net_if_stats[wifi_interface].isup:
                return wifi_interface, "WiFi disabled", False

            # Check if interface has an IP address (connected)
            has_ip = False
            if wifi_interface in net_if_addrs:
                for addr in net_if_addrs[wifi_interface]:
                    if addr.family == 2:  # AF_INET (IPv4)
                        has_ip = True
                        break

            if not has_ip:
                return wifi_interface, "Not connected", False

            # Try to get SSID using iwgetid command
            try:
                result = subprocess.run(
                    ['iwgetid', wifi_interface, '--raw'],
                    capture_output=True,
                    text=True,
                    timeout=2
                )
                if result.returncode == 0 and result.stdout.strip():
                    ssid = result.stdout.strip()

                    # Try to get signal strength
                    signal_result = subprocess.run(
                        ['iwconfig', wifi_interface],
                        capture_output=True,
                        text=True,
                        timeout=2
                    )

                    signal_strength = None
                    if signal_result.returncode == 0:
                        # Parse signal strength from iwconfig output
                        match = re.search(r'Signal level=(-?\d+)', signal_result.stdout)
                        if match:
                            signal_dbm = int(match.group(1))
                            # Convert dBm to percentage (rough approximation)
                            if signal_dbm >= -50:
                                signal_strength = 100
                            elif signal_dbm >= -60:
                                signal_strength = 80
                            elif signal_dbm >= -70:
                                signal_strength = 60
                            elif signal_dbm >= -80:
                                signal_strength = 40
                            else:
                                signal_strength = 20

                    if signal_strength:
                        tooltip = f"Connected to {ssid} ({signal_strength}%)"
                    else:
                        tooltip = f"Connected to {ssid}"

                    return wifi_interface, tooltip, True
                else:
                    return wifi_interface, "Connected (unknown network)", True
            except (subprocess.TimeoutExpired, subprocess.SubprocessError, FileNotFoundError):
                # Fallback if iwgetid/iwconfig not available
                return wifi_interface, "Connected", True

        except Exception:
            return None, "WiFi status unknown", False

    def update_state(self):
        """Update WiFi indicator state"""
        try:
            interface, tooltip, is_connected = self.get_wifi_info()

            if interface is None:
                # No WiFi device
                self.wifi_icon.set_from_file(
                    get_relative_path("../../../config/assets/icons/wifi-off.svg")
                )
            elif is_connected:
                # Connected
                self.wifi_icon.set_from_file(
                    get_relative_path("../../../config/assets/icons/wifi.svg")
                )
            else:
                # WiFi device exists but not connected
                self.wifi_icon.set_from_file(
                    get_relative_path("../../../config/assets/icons/wifi-off.svg")
                )

            self.set_tooltip_text(tooltip)

        except Exception as e:
            # Fallback on error
            self.wifi_icon.set_from_file(
                get_relative_path("../../../config/assets/icons/wifi-off.svg")
            )
            self.set_tooltip_text(f"WiFi error: {str(e)}")

        return True  # Keep the timeout active


class BluetoothIndicator(Button):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        self.bluetooth = BluetoothClient()

        # Use SVG icons instead of font icons
        self.bt_icon = Svg(
            name="bt-icon",
            size=20,
            svg_file=get_relative_path("../../../config/assets/icons/bluetooth.svg"),
        )
        self.add(self.bt_icon)

        self.bluetooth.connect("changed", self.on_bluetooth_changed)
        self.bluetooth.connect("device-added", self.on_device_added)
        self.bluetooth.connect("device-removed", self.on_device_removed)

        self.update_state()

    def update_state(self):
        if not self.bluetooth.enabled:
            self.bt_icon.set_from_file(
                get_relative_path("../../../config/assets/icons/bluetooth-off.svg")
            )
            tooltip = "Bluetooth disabled"
        else:
            connected_devices = self.bluetooth.connected_devices
            if connected_devices:
                self.bt_icon.set_from_file(
                    get_relative_path("../../../config/assets/icons/bluetooth.svg")
                )
                if len(connected_devices) == 1:
                    device = connected_devices[0]
                    tooltip = f"Connected to {device.alias}"
                    if device.battery_percentage > 0:
                        tooltip += f" ({device.battery_percentage:.0f}%)"
                else:
                    tooltip = f"Connected to {len(connected_devices)} devices"
            else:
                self.bt_icon.set_from_file(
                    get_relative_path("../../../config/assets/icons/bluetooth.svg")
                )
                tooltip = "No devices connected"

        self.set_tooltip_text(tooltip)

    def on_bluetooth_changed(self, *args):
        self.update_state()

    def on_device_added(self, _, address):
        self.update_state()

    def on_device_removed(self, _, address):
        self.update_state()


class Battery(Box):
    def __init__(self, **kwargs):
        super().__init__(orientation="v", visible=True, **kwargs)

        self._battery = BatteryService()
        self._battery.changed.connect(self.update_battery)

        self.icon = Svg(
            name="indicators-icon",
            size=24,
            svg_file=get_relative_path(
                "../../../config/assets/icons/battery/battery-100.svg"
            ),
        )
        self.battery_button = Button(
            child=self.icon,
        )

        self.battery_box = Box(
            name="battery-box",
            orientation="h",
            children=[self.battery_button],
        )

        self.battery_button.set_has_tooltip(True)
        self.battery_box.set_has_tooltip(True)

        self.battery_button.connect("query-tooltip", self.on_query_tooltip)
        self.battery_box.connect("query-tooltip", self.on_query_tooltip)

        self.add(self.battery_box)

        GLib.idle_add(self.update_battery)

    def on_query_tooltip(self, *_):
        tooltip = _[-1]
        tooltip.set_markup(self.get_tooltip_text())
        return True

    def get_tooltip_text(self):
        if not self._battery.is_present:
            return "No battery detected"

        percentage = self._battery.percentage
        state = self._battery.state

        capacity = int(float(self._battery.capacity.rstrip("%")))
        tooltip_points = []

        if state == "FULLY_CHARGED":
            status = f"{icons.battery_full} Fully Charged"
        elif state == "CHARGING":
            status = f"{icons.bat_charging} Charging"
        elif percentage <= 15 and state == "DISCHARGING":
            status = f"{icons.bat_alert} Low Battery"
        elif state == "DISCHARGING":
            status = f"{icons.battery_0} Discharging"
        else:
            status = "Battery"
        tooltip_points.append(f"• Status: {status}")

        tooltip_points.append(f"• Level: {int(percentage)}%")
        tooltip_points.append(f"• Battery Health: {capacity}%")

        power_profile = self._battery.power_profile
        if power_profile:
            if power_profile == "power-saver":
                profile_icon = icons.power_saving
            elif power_profile == "performance":
                profile_icon = icons.power_performance
            else:
                profile_icon = icons.power_balanced
            tooltip_points.append(f"• Profile: {profile_icon} {power_profile}")

        temp = self._battery.temperature
        if temp != "N/A":
            tooltip_points.append(f"• Temperature: {temp}")

        if state == "CHARGING":
            time_to_full = self._battery.time_to_full
            if time_to_full:
                tooltip_points.append(f"• Time until full: {time_to_full}")
        elif state == "DISCHARGING":
            time_to_empty = self._battery.time_to_empty
            if time_to_empty:
                tooltip_points.append(f"• Time remaining: {time_to_empty}")

        return "\n".join(tooltip_points)

    def update_battery(self, *_):
        if not self._battery.is_present:
            self.set_visible(False)
            return True

        percentage = self._battery.percentage
        state = self._battery.state
        charging = state in ["CHARGING", "FULLY_CHARGED"]

        if percentage <= 15 and not charging:
            self.icon.add_style_class("alert")
        else:
            self.icon.remove_style_class("alert")

        if state == "FULLY_CHARGED" or (percentage >= 100 and state == "CHARGING"):
            self.icon.set_from_file(
                get_relative_path(
                    "../../../config/assets/icons/battery/battery-100-charging.svg"
                )
            )
        elif state == "CHARGING":
            # Calculate battery level and use corresponding charging icon
            level = min(100, max(0, int(percentage // 10) * 10))
            if level == 0 and percentage > 0:
                level = 10
            icon_name = f"battery-{level:03d}-charging.svg"
            self.icon.set_from_file(
                get_relative_path(f"../../../config/assets/icons/battery/{icon_name}")
            )
        else:
            # Calculate battery level and use corresponding icon
            level = min(100, max(0, int(percentage // 10) * 10))
            if level == 0 and percentage > 0:
                level = 10
            icon_name = f"battery-{level:03d}.svg"
            self.icon.set_from_file(
                get_relative_path(f"../../../config/assets/icons/battery/{icon_name}")
            )

        self.set_visible(True)
        return True


class Indicators(Box):
    def __init__(self, **kwargs):
        super().__init__(
            name="indicators",
            orientation="h",
            spacing=10,
            children=[
                Battery(),
                # WifiIndicator(),
                BluetoothIndicator(),
            ],
            **kwargs,
        )
        self.show_all()
